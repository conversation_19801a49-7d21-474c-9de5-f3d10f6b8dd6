<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <v-row
      v-if="!dateRange"
      class="mb-3 align-center justify-space-between"
    >
      <v-col>
        <h3>
          {{ $t('loyalApp_stats') }}
        </h3>
      </v-col>
      <v-col>
        <date-select
          v-model="internalDateConfig"
          dense
          :show-custom="false"
        />
      </v-col>
    </v-row>
    <h3
      v-else
      class="mb-3"
    >
      {{ $t('loyalApp_stats') }}
    </h3>

    <!-- Unified stats table format -->
    <v-simple-table
      v-if="!isLoading && normalizedStats.length"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <thead>
          <tr>
            <th>{{ $t('common_paymentMethod') }}</th>
            <th>{{ $t('common_type') }}</th>
            <th>{{ $t('common_value') }}</th>
            <th>{{ $t('common_count') }}</th>
            <th>{{ $t('dashboard_average') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="stat in normalizedStats"
            :key="stat.issuer + stat.type"
          >
            <td>{{ stat.issuer }}</td>
            <td>{{ stat.type }}</td>
            <td>
              <currency-formatter
                v-if="currentCurrencySymbol && typeof stat.value === 'number'"
                :value="stat.value"
                :symbol="currentCurrencySymbol"
              />
              <span v-else>{{ stat.value }}</span>
            </td>
            <td>{{ stat.count }}</td>
            <td>
              <currency-formatter
                v-if="currentCurrencySymbol && typeof stat.avg === 'number'"
                :value="stat.avg"
                :symbol="currentCurrencySymbol"
              />
              <span v-else>{{ stat.avg }}</span>
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>

    <!-- Loading state -->
    <v-card-text v-else-if="isLoading">
      <div
        class="d-flex justify-center align-center"
        style="min-height: 100px;"
      >
        <v-progress-circular
          color="primary"
          indeterminate
          size="32"
        />
      </div>
    </v-card-text>

    <!-- No data state -->
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';

export default {
  components: {
    CurrencyFormatter,
    DateSelect,
  },
  props: {
    data: {
      type: [Array, Object],
      default: null,
    },
    currencySymbol: {
      type: String,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    dateRange: {
      type: Object,
      default: null,
    },
    userId: {
      type: Number,
      default: null,
    },
    app: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      internalLoading: false,
      internalStats: null,
      internalCurrencySymbol: '',
      internalDateConfig: {
        interval: 'thisYear',
      },
    };
  },
  computed: {
    isLoading() {
      return this.dateRange ? this.loading : this.internalLoading;
    },
    currentData() {
      return this.data !== null ? this.data : this.internalStats;
    },
    currentCurrencySymbol() {
      return this.currencySymbol || this.internalCurrencySymbol;
    },
    normalizedStats() {
      if (!this.currentData) {
        return [];
      }

      // If data is already in array format (FleetUserStats style)
      if (Array.isArray(this.currentData)) {
        return this.currentData;
      }

      // Convert object format (UserStats style) to array format
      const result = [];
      Object.entries(this.currentData).forEach(([issuer, typeData]) => {
        Object.entries(typeData).forEach(([type, values]) => {
          result.push({
            issuer,
            type,
            value: values.value || 0,
            count: values.count || 0,
            avg: values.avg || 0,
          });
        });
      });

      return result;
    },
  },
  watch: {
    internalDateConfig: {
      handler() {
        if (!this.dateRange) {
          this.fetchData();
        }
      },
      deep: true,
    },
    dateRange: {
      handler(newVal) {
        if (newVal) {
          this.fetchData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    if (!this.data) {
      this.fetchData();
    }
  },
  methods: {
    async fetchData() {
      if (!this.userId || !this.app) {
        return;
      }

      this.internalLoading = true;

      try {
        const url = `/api/gateway/wla-admin/user/${this.userId}/stats`;
        const params = {
          app: this.app,
        };

        if (this.dateRange) {
          // Use external date range
          Object.assign(params, this.dateRange);
        } else {
          // Use internal date config
          params.interval = this.internalDateConfig?.interval;
        }

        const response = await this.axios.get(url, { params });
        const { data } = response;

        this.internalStats = data.stats;
        this.internalCurrencySymbol = data.currency;
      } catch (error) {
        this.snackbar.showMessage('error', this.$t('common_fetchError'));
      } finally {
        this.internalLoading = false;
      }
    },
  },
};
</script>
