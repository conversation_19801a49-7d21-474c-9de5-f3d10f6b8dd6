<template>
  <div>
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      :options.sync="pagination"
      :server-items-length="totalItems"
      :footer-props="footerProps"
      item-key="id"
      :single-expand="true"
      :expanded.sync="expanded"
    >
      <template #item="{ item, expand, isExpanded }">
        <tr>
          <td
            class="cursor-pointer"
            @click="onRowClick({ item, expand, isExpanded })"
          >
            <div class="d-flex align-center">
              <email-formatter :value="item.email" />
              <v-icon
                v-if="item.status === 'l'"
                small
                class="ml-1"
              >
                mdi-lock
              </v-icon>
            </div>
          </td>
          <td
            class="cursor-pointer"
            @click="onRowClick({ item, expand, isExpanded })"
          >
            {{ item.ctime | formatDateDayTime }}
          </td>
          <td
            class="cursor-pointer text-center"
            @click="onRowClick({ item, expand, isExpanded })"
          >
            <v-icon
              :color="item.invitation_accepted ? 'green' : 'red'"
              small
            >
              {{ item.invitation_accepted ? 'mdi-check' : 'mdi-close' }}
            </v-icon>
          </td>
          <td
            class="cursor-pointer"
            @click="onRowClick({ item, expand, isExpanded })"
          >
            <div class="d-flex justify-end">
              <v-tooltip
                v-if="!item.invitation_accepted"
                bottom
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    tile
                    rounded
                    elevation="1"
                    x-small
                    fab
                    color="primary"
                    :loading="item.reinviting"
                    class="mr-1"
                    v-bind="attrs"
                    v-on="on"
                    @click="reinviteUser(item)"
                  >
                    <v-icon small>
                      mdi-email-outline
                    </v-icon>
                  </v-btn>
                </template>
                <span>
                  {{ $t('loyalApp_resendInvitation') }}
                </span>
              </v-tooltip>
              <btn-toggle-lock
                :is-locked="item.status === 'l'"
                :loading="item.statusChanging"
                :text="item.status === 'l' ? $t('actions.unlock') : $t('actions.lock')"
                @click="toggleUserStatus(item)"
              />
            </div>
          </td>
          <td
            class="text-right cursor-pointer"
            @click="onRowClick({ item, expand, isExpanded })"
          >
            <v-icon v-if="isExpanded">
              mdi-chevron-up
            </v-icon>
            <v-icon v-else>
              mdi-chevron-down
            </v-icon>
          </td>
        </tr>
      </template>
      <template #expanded-item="{ headers: _headers, item }">
        <fleet-user-details
          :key="`fleet-user-details-${item.id}`"
          :headers="_headers"
          :app="app"
          :user-id="userId"
          :fleet-user-id="item.id"
          :base-url="baseUrl"
          :params="params"
        />
      </template>
    </v-data-table>
  </div>
</template>

<script>
import EmailFormatter from '@/components/common/formatters/EmailFormatter.vue';
import BtnToggleLock from '@/components/common/button/BtnToggleLock.vue';
import FleetUserDetails from './FleetUserDetails.vue';

export default {
  components: {
    FleetUserDetails,
    EmailFormatter,
    BtnToggleLock,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
    userId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      items: [],
      totalItems: 0,
      expanded: [],
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: [],
        sortDesc: [],
      },
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
    };
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('common_email'),
          value: 'email',
          sortable: false,
        },
        {
          text: this.$t('common_tableCreateDate'),
          value: 'ctime',
          sortable: false,
        },
        {
          text: this.$t('loyalApp_invitationAccepted'),
          value: 'invitation_accepted',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          sortable: false,
          align: 'right',
        },
        {
          value: 'expand',
          sortable: false,
          text: '',
          align: 'right',
        },
      ];
    },
    apiUrl() {
      return `${this.baseUrl}/user/${this.userId}/fleet/users`;
    },
  },
  watch: {
    params: {
      handler(newVal, oldVal) {
        // Skip if old value is empty or undefined
        if (!oldVal || Object.keys(oldVal).length === 0) return;

        // Exclude "interval" from comparison
        const { interval: newInterval, ...newParams } = newVal || {};
        const { interval: oldInterval, ...oldParams } = oldVal || {};
        if (JSON.stringify(newParams) !== JSON.stringify(oldParams)) {
          this.pagination.page = 1;
          this.fetchData();
        }
      },
      deep: true,
      immediate: true,
    },
    pagination: {
      handler(newVal, oldVal) {
        if (oldVal && oldVal.itemsPerPage !== newVal.itemsPerPage) {
          this.pagination.page = 1;
        }
        this.fetchData();
      },
      deep: true,
    },
  },
  methods: {
    onRowClick({ expand, isExpanded }) {
      expand(!isExpanded);
    },
    async fetchData() {
      this.loading = true;
      try {
        // Exclude "interval" from params
        const { interval, ...filteredParams } = this.params || {};
        const params = {
          ...filteredParams,
          page: this.pagination.page,
          perPage: this.pagination.itemsPerPage,
        };
        const response = await this.axios.get(this.apiUrl, {
          params,
        });
        this.items = response.data.items || [];
        this.totalItems = response.data.totalItems || 0;
      } catch {
        this.items = [];
        this.totalItems = 0;
      } finally {
        this.loading = false;
      }
    },
    async toggleUserStatus(item) {
      const newStatus = item.status === 'a' ? 'l' : 'a';

      this.$set(item, 'statusChanging', true);

      try {
        await this.axios.patch(
          `${this.baseUrl}/user/${this.userId}/fleet/users/${item.id}?app=${this.app}`,
          { status: newStatus },
        );

        await this.fetchData();
      } catch (error) {
        this.snackbar.showMessage({
          content: error.response?.data?.message || this.$t('common_fetchError'),
          color: 'error',
        });
      } finally {
        this.$set(item, 'statusChanging', false);
      }
    },
    async reinviteUser(item) {
      this.$set(item, 'reinviting', true);

      try {
        await this.axios.post(
          `${this.baseUrl}/user/${this.userId}/fleet/users/${item.id}/reinvite?app=${this.app}`,
        );

        this.snackbar.showMessage({
          content: this.$t('loyalApp_invitationResent'),
          color: 'success',
        });

        await this.fetchData();
      } catch (error) {
        this.snackbar.showMessage({
          content: error.response?.data?.message || this.$t('common_fetchError'),
          color: 'error',
        });
      } finally {
        this.$set(item, 'reinviting', false);
      }
    },
  },
};
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
</style>
