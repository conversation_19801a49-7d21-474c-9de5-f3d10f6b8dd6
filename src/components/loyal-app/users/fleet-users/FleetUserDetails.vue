<template>
  <td
    :colspan="headers.length"
  >
    <div>
      <div
        v-if="loading"
        class="d-flex justify-center align-center pa-2"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <div
        v-else-if="error"
        class="text-center"
      >
        <v-icon
          color="error"
          class="mb-2"
        >
          mdi-alert-circle-outline
        </v-icon>
        <div class="error--text">
          {{ error }}
        </div>
      </div>
      <div v-else>
        <user-stats-table
          v-if="details.stats && details.stats.length > 0"
          :data="details.stats"
          :currency-symbol="details.currency.symbol"
          :loading="loading"
          :date-range="params"
        />
        <user-limits-table
          v-if="details.limit"
          :data="details.limit"
          :currency-symbol="details.currency.symbol"
          :editable="true"
          :edit-props="{
            email: details.email,
            app: app,
            userId: userId,
            fleetUserId: fleetUserId,
            baseUrl: baseUrl
          }"
          @limits-updated="onLimitsUpdated"
        />
      </div>
    </div>
  </td>
</template>

<script>
import UserStatsTable from '../UserStatsTable.vue';
import UserLimitsTable from '../UserLimitsTable.vue';

export default {
  components: {
    UserStatsTable,
    UserLimitsTable,
  },
  props: {
    headers: {
      type: Array,
      required: true,
    },
    app: {
      type: String,
      required: true,
    },
    userId: {
      type: Number,
      required: true,
    },
    fleetUserId: {
      type: Number,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      error: '',
      details: {
        stats: [],
        limit: {},
        currency: {},
        email: '',
      },
    };
  },
  watch: {
    fleetUserId: {
      handler() {
        this.fetchDetails();
      },
      immediate: false,
    },
    params: {
      handler(newVal, oldVal) {
        // Skip if old value is empty or undefined
        if (!oldVal || Object.keys(oldVal).length === 0) return;

        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.fetchDetails();
        }
      },
      deep: true,
      immediate: false,
    },
  },
  mounted() {
    this.fetchDetails();
  },
  methods: {
    async fetchDetails() {
      this.loading = true;
      this.error = '';
      try {
        const response = await this.axios.get(
          `${this.baseUrl}/user/${this.userId}/fleet/users/${this.fleetUserId}`,
          { params: this.params },
        );
        this.details.stats = response.data.stats || [];
        this.details.limit = response.data.limit || {};
        this.details.currency = response.data.currency || {};
        this.details.email = response.data.email || '';
      } catch (e) {
        this.error = this.$t('common_fetchError');
      } finally {
        this.loading = false;
      }
    },
    onLimitsUpdated() {
      this.fetchDetails();
    },
  },
};
</script>
