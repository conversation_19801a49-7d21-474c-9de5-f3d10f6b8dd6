<template>
  <div>
    <btn-notification
      :text="$t(buttonTitle)"
      @click="open"
    />

    <v-dialog
      v-model="dialog"
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t(buttonTitle) }}{{ email ? ` - ${email}` : '' }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="close"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="pt-6">
          <v-form
            ref="form"
            v-model="form.valid"
            :lazy-validation="true"
          >
            <v-container
              fluid
              class="px-0 py-0"
            >
              <v-row no-gutters>
                <v-col cols="12">
                  <v-text-field
                    v-model="notification.title"
                    prepend-icon="mdi-format-title"
                    :label="$t('common_notificationTitle')"
                    :rules="form.validationRules.title"
                    required
                    autofocus
                  />
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="notification.body"
                    prepend-icon="mdi-message-text"
                    :label="$t('common_notificationContent')"
                    :rules="form.validationRules.body"
                    required
                  />
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>

        <v-card-actions class="pt-0">
          <v-spacer />
          <v-btn
            color="primary"
            text
            @click="close"
          >
            {{ $t('actions.cancel') }}
          </v-btn>
          <v-btn
            color="primary"
            :disabled="!isFormFilled || loaders.submit"
            :loading="loaders.submit"
            @click="sendNotification"
          >
            {{ $t('actions.send') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import BtnNotification from '@/components/common/button/BtnNotification.vue';

export default {
  components: {
    BtnNotification,
  },
  props: {
    url: {
      type: String,
      required: true,
    },
    buttonTitle: {
      type: String,
      default: 'common_sendNotification',
    },
    email: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dialog: false,
      notification: {
        title: '',
        body: '',
      },
      form: {
        valid: true,
        validationRules: {
          title: [
            (v) => !!v || this.$t('common_fieldRequired'),
          ],
          body: [
            (v) => !!v || this.$t('common_fieldRequired'),
          ],
        },
      },
      loaders: {
        submit: false,
      },
    };
  },
  computed: {
    isFormFilled() {
      return !!this.notification.title && !!this.notification.body;
    },
  },
  watch: {
    dialog(newValue) {
      if (!newValue && this.$refs.form) {
        this.resetForm();
      }
    },
  },
  methods: {
    open() {
      this.dialog = true;
    },
    resetForm() {
      this.$refs.form.reset();
      this.notification = {
        title: '',
        body: '',
      };
      this.loaders.submit = false;
    },
    close() {
      this.dialog = false;
      this.resetForm();
    },
    async sendNotification() {
      if (this.$refs.form.validate()) {
        try {
          this.loaders.submit = true;
          await this.axios.post(
            this.url,
            { title: this.notification.title, body: this.notification.body },
          );

          this.$emit('success', this.$t('common_success'));
          this.close();
        } catch (error) {
          this.$emit('error', this.$t('common_error_occurred'));
        } finally {
          this.loaders.submit = false;
        }
      }
    },
  },
};
</script>
